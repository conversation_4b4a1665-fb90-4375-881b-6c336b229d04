<template>
  <div class="card-wrapper">
    <a-card
      class="asset-card"
      :bordered="false"
      @contextmenu.prevent="handleContextMenu"
      @click="handleClick"
      @dblclick="handleDoubleClick"
    >
      <div class="asset-card-content">
        <div class="asset-icon">
          <DatabaseOutlined style="font-size: 24px" />
          <div
            v-if="asset.asset_type === 'organization'"
            class="enterprise-indicator"
          >
            <ApiOutlined />
          </div>
        </div>
        <div class="asset-info">
          <div class="asset-name">{{ asset.title }}</div>
          <div class="asset-type">
            {{ t('personal.hostType') }}{{ asset.username ? ', ' + asset.username : '' }}
          </div>
        </div>
        <div
          class="edit-icon"
          @click.stop="handleEdit"
        >
          <EditOutlined />
        </div>
      </div>
    </a-card>

    <!-- 右键菜单 -->
    <AssetContextMenu
      v-if="showContextMenu"
      :visible="showContextMenu"
      :position="contextMenuPosition"
      :asset="asset"
      @close="showContextMenu = false"
      @connect="handleConnect"
      @edit="handleEdit"
      @refresh="handleRefresh"
      @remove="handleRemove"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { DatabaseOutlined, EditOutlined, ApiOutlined } from '@ant-design/icons-vue'
import AssetContextMenu from './AssetContextMenu.vue'
import i18n from '@/locales'

const { t } = i18n.global

// Props
interface AssetNode {
  key: string
  title: string
  favorite?: boolean
  ip?: string
  uuid?: string
  username?: string
  asset_type?: string
  [key: string]: any
}

interface Props {
  asset: AssetNode
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'click': [asset: AssetNode]
  'double-click': [asset: AssetNode]
  'edit': [asset: AssetNode]
  'connect': [asset: AssetNode]
  'refresh': [asset: AssetNode]
  'remove': [asset: AssetNode]
}>()

// State
const showContextMenu = ref(false)
const contextMenuPosition = reactive({ x: 0, y: 0 })

// Methods
const handleClick = () => {
  emit('click', props.asset)
}

const handleDoubleClick = () => {
  emit('double-click', props.asset)
}

const handleEdit = () => {
  emit('edit', props.asset)
}

const handleConnect = () => {
  emit('connect', props.asset)
  showContextMenu.value = false
}

const handleRefresh = () => {
  emit('refresh', props.asset)
  showContextMenu.value = false
}

const handleRemove = () => {
  emit('remove', props.asset)
  showContextMenu.value = false
}

const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  contextMenuPosition.x = event.clientX
  contextMenuPosition.y = event.clientY
  showContextMenu.value = true

  const closeMenu = () => {
    showContextMenu.value = false
    document.removeEventListener('click', closeMenu)
  }

  setTimeout(() => {
    document.addEventListener('click', closeMenu)
  }, 0)
}
</script>

<style lang="less" scoped>
.card-wrapper {
  margin-bottom: 0;
  transition: width 0.3s ease;
}

.asset-card {
  position: relative;
  padding-right: 36px;
  background-color: var(--bg-color-secondary);
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--hover-bg-color);
  }

  :deep(.ant-card-body) {
    padding: 8px 12px;
  }
}

.edit-icon {
  position: absolute;
  top: 50%;
  right: 24px;
  transform: translateY(-50%);
  color: var(--text-color-tertiary);
  font-size: 22px;
  opacity: 0;
  pointer-events: none;
  transition:
    opacity 0.2s ease,
    color 0.2s ease;
}

.asset-card:hover .edit-icon {
  opacity: 1;
  pointer-events: auto;
}

.edit-icon:hover {
  color: #1890ff;
}

.asset-card-content {
  display: flex;
  align-items: center;
  min-height: 48px;
}

.asset-icon {
  width: 32px;
  height: 32px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  margin-right: 8px;
  position: relative;
}

.enterprise-indicator {
  position: absolute;
  top: -6px;
  left: -6px;
  width: 14px;
  height: 14px;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--bg-color);

  :deep(.anticon) {
    font-size: 8px;
    color: white;
  }
}

.asset-info {
  flex: 1;
  overflow: hidden;
}

.asset-name {
  font-size: 13px;
  font-weight: bold;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.asset-type {
  font-size: 12px;
  color: var(--text-color-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
